<template>
  <div v-loading="isLoading" class="w-full h-full bg-[#003366] p-4 text-white font-sans overflow-y-auto">
    <div v-if="isListEmpty">
      <h1 class="text-2xl font-bold mb-6 text-center">暂无预警记录</h1>
    </div>
    <div v-else-if="!record">
      <h1 class="text-2xl font-bold mb-6 text-center">请先选择一条预警记录</h1>
    </div>
    <div v-else>
      <h1 class="text-2xl font-bold mb-6 text-center">预警详情</h1>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Top-Left Cell: Basic Info & Special Event Info -->
        <div class="flex flex-col space-y-6">
          <!-- 基本信息 -->
          <section class="bg-[#004488] p-4 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-3 border-l-4 border-blue-400 pl-3">基本信息</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-3 text-sm">
              <div>结构物名称：<span :class="valueClass">{{ basicData.structureName }}</span></div>
              <div>管养单位：<span :class="valueClass">{{ basicData.managementUnit }}</span></div>
              <div>联系人：<span :class="valueClass">{{ basicData.contactPerson }}</span></div>
              <div>联系电话：<span :class="valueClass">{{ basicData.contactPhone }}</span></div>
            </div>
          </section>

          <!-- 特殊事件信息 -->
          <section class="bg-[#004488] p-4 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-3 border-l-4 border-blue-400 pl-3">特殊事件信息</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-3 text-sm">
              <div>事件类型：<span :class="valueClass">{{ eventData.eventType }}</span></div>
              <div>事件名称：<span :class="valueClass">{{ eventData.eventName }}</span></div>
              <div>事件状态：<span :class="valueClass">{{ eventData.eventStatus }}</span></div>
              <div>事件发生时间：<span :class="valueClass">{{ eventData.eventTime }}</span></div>
            </div>
            <div class="mt-3 text-sm">
              <p class="font-medium">事件说明：</p>
              <div class="bg-[#12698D] p-3 rounded min-h-[50px] mt-1">{{ eventData.eventDescription }}</div>
            </div>
          </section>
        </div>

        <!-- Top-Right Cell: Disposal Process -->
        <section class="bg-[#004488] p-4 rounded-lg shadow">
          <h2 class="text-xl font-semibold mb-3 border-l-4 border-blue-400 pl-3">处置流程</h2>
          <div class="relative pl-5 pt-2">
            <!-- Timeline Vertical Line -->
            <div class="absolute left-2 top-3 bottom-3 w-0.5 bg-gray-500 opacity-75"></div>

            <div v-for="(event, index) in disposalProcessTimeline" :key="index" class="mb-5 relative last:mb-0">
              <div class="absolute left-[-23px] top-[2px] w-4 h-4 rounded-full border-2 border-[#003366]" :class="event.isCurrent ? 'bg-orange-400' : 'bg-blue-400'"></div>
              <p class="font-semibold text-base">{{ event.title }}</p>
              <p v-for="detail in event.details" :key="detail" class="text-xs text-gray-300 leading-tight">{{ detail }}</p>
              <p class="text-xs text-gray-400 mt-0.5">{{ event.time }}</p>
            </div>
          </div>
        </section>

        <!-- Bottom-Left Cell: Field Record -->
        <section class="bg-[#004488] p-4 rounded-lg shadow">
          <h2 class="text-xl font-semibold mb-3 border-l-4 border-blue-400 pl-3">现场记录</h2>
          <div class="grid grid-cols-2 gap-4 mt-20">
            <div>
              <div class="w-full h-auto rounded aspect-[4/3] bg-[#0055AA] flex items-center justify-center">
                <img v-if="fieldRecordMedia.inProgress.imageSrc" :src="fieldRecordMedia.inProgress.imageSrc" :alt="fieldRecordMedia.inProgress.altText" class="w-full h-full object-cover">
                <span v-else>暂无图片</span>
              </div>
              <p class="text-center mt-2 text-sm">{{ fieldRecordMedia.inProgress.caption }}</p>
            </div>
            <div>
              <div class="w-full h-auto rounded aspect-[4/3] bg-[#0055AA] flex items-center justify-center">
                <img v-if="fieldRecordMedia.afterEvent.imageSrc" :src="fieldRecordMedia.afterEvent.imageSrc" :alt="fieldRecordMedia.afterEvent.altText" class="w-full h-full object-cover">
                <span v-else>暂无图片</span>
              </div>
              <p class="text-center mt-2 text-sm">{{ fieldRecordMedia.afterEvent.caption }}</p>
            </div>
          </div>
        </section>

        <!-- Bottom-Right Cell: Disposal Record -->
        <section class="bg-[#004488] p-4 rounded-lg shadow">
          <h2 class="text-xl font-semibold mb-3 border-l-4 border-blue-400 pl-3">处置记录</h2>
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 text-sm mb-3">
            <div>处置人员：<span :class="valueClass">{{ disposalRecordData.personnel }}</span></div>
            <div>处置时间：<span :class="valueClass">{{ disposalRecordData.time }}</span></div>
            <div class="col-span-2">联系电话：<span :class="valueClass">{{ disposalRecordData.contactPhone }}</span></div>
          </div>
          <p class="font-medium text-sm">处置照片：</p>
          <div class="w-full h-56 p-2 pb-5 ">
            <div class="w-full h-full flex justify-center items-center">
              <img v-if="disposalRecordData.photoSrc" class="w-auto h-full" :src="disposalRecordData.photoSrc" :alt="disposalRecordData.photoAlt">
              <span v-else>暂无图片</span>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
// @ts-nocheck
import { computed } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  record: {
    type: Object,
    default: () => null,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  isListEmpty: {
    type: Boolean,
    default: false,
  }
});

const formatTimestamp = (timestampStr) => {
  if (!timestampStr) return '-';
  const timestampNum = Number(timestampStr);
  if (isNaN(timestampNum)) return timestampStr; // Return original string if not a number

  // Check if it's seconds (10 digits) or milliseconds (13 digits)
  if (String(timestampNum).length === 10) {
    return dayjs.unix(timestampNum).format('YYYY-MM-DD HH:mm:ss');
  }
  return dayjs(timestampNum).format('YYYY-MM-DD HH:mm:ss');
};

// Component Data
const basicData = computed(() => {
  if (!props.record) return {};
  return {
    structureName: props.record.structureName || '-',
    managementUnit: props.record.managementName || '-',
    contactPerson: props.record.managementUser || '-',
    contactPhone: props.record.managementPhone || '-',
  };
});

const eventData = computed(() => {
  if (!props.record) return {};
  return {
    eventType: '特殊事件', // Assuming this based on page context
    eventName: props.record.monitorContent || '-',
    eventStatus: props.record.handleTime ? '已处置' : '处置中',
    eventTime: formatTimestamp(props.record.alarmStartTime),
    eventDescription: props.record.monitorContent || '无',
  };
});

const fieldRecordMedia = computed(() => {
  if (!props.record) return { inProgress: {}, afterEvent: {} };
  return {
    inProgress: {
      imageSrc: props.record.accidentDuringPic,
      altText: '事中现场照片',
      caption: '事中',
    },
    afterEvent: {
      imageSrc: props.record.accidentAfterPic,
      altText: '事后现场照片',
      caption: '事后',
    },
  };
});

const disposalProcessTimeline = computed(() => {
  if (!props.record) return [];
  const timeline = [];

  if (props.record.alarmStartTime) {
    timeline.push({
      title: '事件发生时间',
      details: [],
      time: formatTimestamp(props.record.alarmStartTime),
    });
  }

  if (props.record.reportTime) {
    const details = [];
    if (props.record.reportUser) details.push(`上报人：${props.record.reportUser}`);
    if (props.record.reportUserTel) details.push(`联系电话：${props.record.reportUserTel}`);
    timeline.push({
      title: '预警上报到市级',
      details,
      time: formatTimestamp(props.record.reportTime),
    });
  }

  const isHandled = !!props.record.handleTime;
  const title = isHandled ? '已处置' : '处置中';

  const details = [];
  if (props.record.handleUser) details.push(`处置人员：${props.record.handleUser}`);
  else if (!isHandled) details.push('处置人员： -');

  if (props.record.handleUserTel) details.push(`联系电话：${props.record.handleUserTel}`);
  else if (!isHandled) details.push('联系电话： -');
  
  if (props.record.handleContent) details.push(`处置措施：${props.record.handleContent}`);
  else if (!isHandled) details.push('处置措施： -');


  if (isHandled || details.length > 0) {
     timeline.push({
      title,
      details,
      time: isHandled ? formatTimestamp(props.record.handleTime) : '-',
      isCurrent: !isHandled,
    });
  }

  // Add more timeline events based on other properties in props.record if available
  // e.g., props.record.disposalTime, props.record.completionTime etc.

  return timeline;
});

const disposalRecordData = computed(() => {
  if (!props.record) return {};
  return {
    personnel: props.record.handleUser || '-',
    time: props.record.handleTime ? formatTimestamp(props.record.handleTime) : '-',
    contactPhone: props.record.handleUserTel || '-',
    photoSrc: props.record.disposalPic,
    photoAlt: '处置照片',
  };
});


const valueClass = 'text-cyan-300 font-medium';
</script>

<style scoped>
/* Scoped styles for Detail.vue can go here */
</style>
