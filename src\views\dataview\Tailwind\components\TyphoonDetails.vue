<template>
  <div v-show="selectedStormId" class="bg-dark-card border border-dark-border rounded-xl p-4 card-shadow">
    <div class="flex justify-between items-center mb-4">
      <div>
        <h2 class="text-2xl font-bold flex items-center">
          <i class="fa fa-map-marker text-danger mr-2"></i>
          <span>{{ selectedStormName }}</span>
          <span class="ml-2 bg-danger/20 text-danger text-sm px-2 py-1 rounded-full">活跃中</span>
        </h2>
        <p class="text-gray-400 text-base mt-1">台风编号: {{ selectedStormId }}</p>
      </div>
      <button
        @click="refreshStormData"
        class="bg-primary/20 hover:bg-primary/30 text-primary px-3 py-2 rounded-lg transition-all duration-300 flex items-center"
      >
        <i class="fa fa-refresh mr-2"></i>刷新
      </button>
    </div>

    <!-- 台风数据指标 - 两列布局 -->
    <div class="grid grid-cols-2 gap-4 mb-6">
      <div class="bg-dark rounded-lg p-4">
        <div class="text-gray-400 text-base mb-2">当前位置</div>
        <div class="text-xl font-bold">{{ currentLocation }}</div>
      </div>
      <div class="bg-dark rounded-lg p-4">
        <div class="text-gray-400 text-base mb-2">台风等级</div>
        <div class="text-xl font-bold">{{ typhoonType }}</div>
      </div>
      <div class="bg-dark rounded-lg p-4">
        <div class="text-gray-400 text-base mb-2">中心气压</div>
        <div class="text-xl font-bold">{{ currentPressure }}</div>
      </div>
      <div class="bg-dark rounded-lg p-4">
        <div class="text-gray-400 text-base mb-2">最大风速</div>
        <div class="text-xl font-bold">{{ currentWindSpeed }}</div>
      </div>
    </div>

    <!-- 台风未来趋势 -->
<div class="bg-dark rounded-lg p-3">
  <h3 class="text-lg font-medium mb-2">未来趋势</h3>
  <div class="grid grid-cols-3 gap-2">
    <div class="bg-dark/70 rounded-md p-2">
      <div class="text-sm text-gray-400 mb-1">12小时后</div>
      <div class="text-sm">
        <span>位置: {{ forecast12h.location }}</span><br>
        <span>风速: {{ forecast12h.wind }} m/s</span>
      </div>
    </div>
    <div class="bg-dark/70 rounded-md p-2">
      <div class="text-sm text-gray-400 mb-1">24小时后</div>
      <div class="text-sm">
        <span>位置: {{ forecast24h.location }}</span><br>
        <span>风速: {{ forecast24h.wind }} m/s</span>
      </div>
    </div>
    <div class="bg-dark/70 rounded-md p-2">
      <div class="text-sm text-gray-400 mb-1">48小时后</div>
      <div class="text-sm">
        <span>位置: {{ forecast48h.location }}</span><br>
        <span>风速: {{ forecast48h.wind }} m/s</span>
      </div>
    </div>
  </div>
</div>

  </div>
</template>

<script setup>
// Props
const props = defineProps({
  selectedStormId: {
    type: String,
    default: null
  },
  selectedStormName: {
    type: String,
    default: '台风名称'
  },
  currentLocation: {
    type: String,
    default: '--'
  },
  typhoonType: {
    type: String,
    default: '--'
  },
  currentPressure: {
    type: String,
    default: '-- hPa'
  },
  currentWindSpeed: {
    type: String,
    default: '-- m/s'
  },
  forecast12h: {
    type: Object,
    default: () => ({ location: '--', wind: '--' })
  },
  forecast24h: {
    type: Object,
    default: () => ({ location: '--', wind: '--' })
  },
  forecast48h: {
    type: Object,
    default: () => ({ location: '--', wind: '--' })
  }
})

// Emits
const emit = defineEmits(['refresh-storm-data'])

// Methods
const refreshStormData = () => {
  emit('refresh-storm-data')
}
</script>

<style scoped>
/* Tailwind自定义样式 */
.bg-dark {
  background-color: #0D1117;
}

.bg-dark-card {
  background-color: #161B22;
}

.border-dark-border {
  border-color: #30363D;
}

.text-primary {
  color: #165DFF;
}

.text-danger {
  color: #F53F3F;
}

.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}
</style>
