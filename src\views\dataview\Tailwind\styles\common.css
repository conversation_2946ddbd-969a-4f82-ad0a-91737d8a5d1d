/* 通用样式定义 */

/* 全局字体大小调整 */
body {
  font-size: 16px; /* 增大基础字体大小 */
}

/* 增大所有文本的基础大小 */
.text-xs {
  font-size: 0.875rem !important; /* 原来是 0.75rem */
}

.text-sm {
  font-size: 1rem !important; /* 原来是 0.875rem */
}

.text-base {
  font-size: 1.125rem !important; /* 原来是 1rem */
}

.text-lg {
  font-size: 1.25rem !important; /* 原来是 1.125rem */
}

.text-xl {
  font-size: 1.5rem !important; /* 原来是 1.25rem */
}

.text-2xl {
  font-size: 1.875rem !important; /* 原来是 1.5rem */
}

.text-3xl {
  font-size: 2.25rem !important; /* 原来是 1.875rem */
}

/* 背景色 */
.bg-dark {
  background-color: #0D1117;
}

.bg-dark-card {
  background-color: #161B22;
}

.bg-dark-border {
  background-color: #30363D;
}

/* 边框色 */
.border-dark-border {
  border-color: #30363D;
}

/* 文字颜色 */
.text-primary {
  color: #165DFF;
}

.text-secondary {
  color: #36BFFA;
}

.text-warning {
  color: #FF7D00;
}

.text-danger {
  color: #F53F3F;
}

.text-success {
  color: #00B42A;
}

/* 渐变文字 */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, #165DFF, #36BFFA);
}

/* 阴影效果 */
.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}

/* 台风激活状态 */
.typhoon-active {
  border-left: 4px solid #F53F3F !important;
  background-color: rgba(22, 27, 34, 0.8) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(22, 93, 255, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(22, 93, 255, 0.7);
}

/* 动画效果 */
.typhoon-item {
  transition: all 0.3s ease;
}

.typhoon-item:hover {
  transform: translateY(-2px);
}

/* 图表容器 */
.echarts {
  width: 100% !important;
  height: 100% !important;
}

/* 响应式调整 */
@media (max-width: 1280px) {
  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-3 {
    grid-column: span 1 / span 1;
  }

  /* 在小屏幕上调整高度 */
  .space-y-4 > * {
    margin-bottom: 1rem;
  }
}
