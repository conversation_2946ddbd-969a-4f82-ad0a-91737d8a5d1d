import { TYPHOON_TYPE_MAPPING } from '../config/constants'

/**
 * 台风数据处理工具类
 */
export class TyphoonDataProcessor {
  /**
   * 处理台风详情数据
   * @param {Array} forecast - 预测数据
   * @returns {Object} 处理后的台风详情
   */
  static processStormDetails(forecast) {
    if (!forecast || forecast.length === 0) {
      return {
        current: {
          location: '--',
          pressure: '-- hPa',
          windSpeed: '-- m/s',
          type: '--'
        },
        forecast12h: { location: '--', wind: '--' },
        forecast24h: { location: '--', wind: '--' },
        forecast48h: { location: '--', wind: '--' }
      }
    }

    try {
      const current = forecast[0]
      
      const result = {
        current: {
          location: current.lat && current.lon
            ? `${current.lat}°N, ${current.lon}°E`
            : '--',
          pressure: current.pressure
            ? `${current.pressure} hPa`
            : '-- hPa',
          windSpeed: current.windSpeed
            ? `${current.windSpeed} m/s`
            : '-- m/s',
          type: current.type
            ? (TYPHOON_TYPE_MAPPING[current.type] || current.type)
            : '--'
        },
        forecast12h: { location: '--', wind: '--' },
        forecast24h: { location: '--', wind: '--' },
        forecast48h: { location: '--', wind: '--' }
      }

      // 处理12小时预测
      if (forecast.length > 1) {
        const forecast12hData = forecast[1]
        result.forecast12h = {
          location: forecast12hData.lat && forecast12hData.lon
            ? `${forecast12hData.lat}°N, ${forecast12hData.lon}°E`
            : '--',
          wind: forecast12hData.windSpeed || '--'
        }
      }

      // 处理24小时预测
      if (forecast.length > 2) {
        const forecast24hData = forecast[2]
        result.forecast24h = {
          location: forecast24hData.lat && forecast24hData.lon
            ? `${forecast24hData.lat}°N, ${forecast24hData.lon}°E`
            : '--',
          wind: forecast24hData.windSpeed || '--'
        }
      }

      // 处理48小时预测
      if (forecast.length > 4) {
        const forecast48hData = forecast[4]
        result.forecast48h = {
          location: forecast48hData.lat && forecast48hData.lon
            ? `${forecast48hData.lat}°N, ${forecast48hData.lon}°E`
            : '--',
          wind: forecast48hData.windSpeed || '--'
        }
      }

      return result
    } catch (error) {
      console.error('处理台风详情数据时出错:', error)
      return {
        current: {
          location: '--',
          pressure: '-- hPa',
          windSpeed: '-- m/s',
          type: '--'
        },
        forecast12h: { location: '--', wind: '--' },
        forecast24h: { location: '--', wind: '--' },
        forecast48h: { location: '--', wind: '--' }
      }
    }
  }

  /**
   * 验证坐标数据
   * @param {number} lat - 纬度
   * @param {number} lon - 经度
   * @returns {boolean} 是否有效
   */
  static validateCoordinates(lat, lon) {
    return !isNaN(lat) && !isNaN(lon) && 
           lat >= -90 && lat <= 90 && 
           lon >= -180 && lon <= 180
  }

  /**
   * 格式化坐标
   * @param {number} lat - 纬度
   * @param {number} lon - 经度
   * @returns {string} 格式化后的坐标
   */
  static formatCoordinates(lat, lon) {
    if (!this.validateCoordinates(lat, lon)) {
      return '--'
    }
    return `${lat}°N, ${lon}°E`
  }

  /**
   * 获取台风等级中文名称
   * @param {string} type - 台风等级代码
   * @returns {string} 中文名称
   */
  static getTyphoonTypeName(type) {
    return TYPHOON_TYPE_MAPPING[type] || type || '--'
  }

  /**
   * 验证台风数据完整性
   * @param {Object} storm - 台风数据
   * @returns {boolean} 是否完整
   */
  static validateStormData(storm) {
    return storm && 
           typeof storm.id === 'string' && 
           typeof storm.name === 'string' && 
           storm.id.length > 0 && 
           storm.name.length > 0
  }

  /**
   * 验证预测数据完整性
   * @param {Array} forecast - 预测数据
   * @returns {boolean} 是否完整
   */
  static validateForecastData(forecast) {
    return Array.isArray(forecast) && 
           forecast.length > 0 && 
           forecast.every(point => 
             point && 
             typeof point === 'object' && 
             (point.lat !== undefined || point.lon !== undefined)
           )
  }

  /**
   * 清理和标准化预测数据
   * @param {Array} forecast - 原始预测数据
   * @returns {Array} 清理后的预测数据
   */
  static cleanForecastData(forecast) {
    if (!Array.isArray(forecast)) {
      return []
    }

    return forecast.filter(point => {
      if (!point || typeof point !== 'object') {
        return false
      }

      const lat = parseFloat(point.lat)
      const lon = parseFloat(point.lon)
      
      return this.validateCoordinates(lat, lon)
    }).map(point => ({
      ...point,
      lat: parseFloat(point.lat),
      lon: parseFloat(point.lon),
      pressure: point.pressure ? parseFloat(point.pressure) : null,
      windSpeed: point.windSpeed ? parseFloat(point.windSpeed) : null
    }))
  }
}

/**
 * 设备数据处理工具类
 */
export class DeviceDataProcessor {
  /**
   * 验证设备数据
   * @param {Object} deviceData - 设备数据
   * @returns {boolean} 是否有效
   */
  static validateDeviceData(deviceData) {
    if (!deviceData || typeof deviceData !== 'object') {
      return false
    }

    const requiredFields = [
      'temperature', 'humidity', 'windSpeed', 'windDirection',
      'pressure', 'rainfall', 'waveHeight', 'visibility'
    ]

    return requiredFields.every(field => 
      deviceData.hasOwnProperty(field) && 
      deviceData[field] !== null && 
      deviceData[field] !== undefined
    )
  }

  /**
   * 格式化设备数值
   * @param {number|string} value - 数值
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的数值
   */
  static formatDeviceValue(value, decimals = 1) {
    const num = parseFloat(value)
    if (isNaN(num)) {
      return '--'
    }
    return num.toFixed(decimals)
  }

  /**
   * 获取风向文本
   * @param {number} direction - 风向角度
   * @param {Array} windDirections - 风向数组
   * @returns {string} 风向文本
   */
  static getWindDirectionText(direction, windDirections) {
    if (isNaN(direction) || !Array.isArray(windDirections)) {
      return '未知'
    }

    const normalizedDirection = ((direction % 360) + 360) % 360
    const index = Math.floor(normalizedDirection / 22.5)
    return windDirections[index] || '未知'
  }
}
