// API 配置
export const API_CONFIG = {
  stormListUrl: 'https://devapi.qweather.com/v7/tropical/storm-list',
  stormForecastUrl: 'https://devapi.qweather.com/v7/tropical/storm-forecast',
  key: '005ad0bad9f442e6ac4df7d29a73ffa5',
  basin: 'NP',
  year: '2025'
}

// 台风等级映射
export const TYPHOON_TYPE_MAPPING = {
  "TD": "热带低压",
  "TS": "热带风暴",
  "STS": "强热带风暴",
  "TY": "台风",
  "STY": "强台风",
  "SuperTY": "超强台风"
}

// 颜色配置
export const COLORS = {
  primary: '#165DFF',
  secondary: '#36BFFA',
  warning: '#FF7D00',
  danger: '#F53F3F',
  success: '#00B42A',
  purple: '#A855F7',
  cyan: '#06B6D4',
  yellow: '#EAB308',
  blue: '#3B82F6'
}

// 风向映射
export const WIND_DIRECTIONS = [
  '北', '东北偏北', '东北', '东北偏东', 
  '东', '东南偏东', '东南', '东南偏南', 
  '南', '西南偏南', '西南', '西南偏西', 
  '西', '西北偏西', '西北', '西北偏北'
]

// 设备类型配置
export const DEVICE_TYPES = {
  TEMPERATURE: {
    id: 'TEMP-001',
    icon: 'fa-thermometer-half',
    color: 'warning',
    unit: '°C',
    name: '温度监测'
  },
  HUMIDITY: {
    id: 'HUM-001',
    icon: 'fa-tint',
    color: 'secondary',
    unit: '%',
    name: '湿度监测'
  },
  WIND_SPEED: {
    id: 'WIND-001',
    icon: 'fa-wind',
    color: 'primary',
    unit: 'm/s',
    name: '风速监测'
  },
  WIND_DIRECTION: {
    id: 'WDIR-001',
    icon: 'fa-compass',
    color: 'purple-500',
    unit: '°',
    name: '风向监测'
  },
  PRESSURE: {
    id: 'PRESS-001',
    icon: 'fa-cloud',
    color: 'gray-400',
    unit: 'hPa',
    name: '气压监测'
  },
  RAINFALL: {
    id: 'RAIN-001',
    icon: 'fa-tint',
    color: 'blue-400',
    unit: 'mm',
    name: '降雨量监测'
  },
  WAVE: {
    id: 'WAVE-001',
    icon: 'fa-water',
    color: 'cyan-400',
    unit: 'm',
    name: '海浪高度'
  },
  VISIBILITY: {
    id: 'VIS-001',
    icon: 'fa-eye',
    color: 'yellow-400',
    unit: 'km',
    name: '能见度'
  }
}
