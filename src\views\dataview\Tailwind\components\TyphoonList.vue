<template>
  <div class="bg-dark-card border border-dark-border rounded-xl p-4 card-shadow">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold flex items-center">
        <i class="fa fa-list-ul text-primary mr-2"></i>活跃台风
      </h2>
      <span class=" text-[#F53F3F] text-sm px-2 py-1 rounded-full">
        <i class="fa fa-exclamation-circle mr-1"></i>
        <span>{{ activeStormCount }}</span>个
      </span>
    </div>
    <div class="flex">
      <div v-for="storm in stormList" :key="storm.id" class="typhoon-item cursor-pointer rounded-lg transition-all duration-200 p-3 border" :class="{
        'border-[#F53F3F] ': storm.id === selectedStormId,
        'border-[#30363D] hover:bg-gray-800  ': storm.id !== selectedStormId
      }" @click="selectStorm(storm.id)">
        <div class="text-center">
          <!-- 台风名称 -->
          <div class="font-medium text-base mb-1">
            {{ storm.name }}
            <span v-if="storm.isActive === '1'" class="ml-1 text-[#F53F3F] text-sm">
              <i class="fa fa-circle"></i>
            </span>
          </div>

          <!-- 台风编号 -->
          <div class="text-gray-400 text-sm">{{ storm.id }}</div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  stormList: {
    type: Array,
    default: () => []
  },
  selectedStormId: {
    type: String,
    default: null
  }
})

// Emits
const emit = defineEmits(['select-storm'])

// Computed
const activeStormCount = computed(() => props.stormList.length)

// Methods
const selectStorm = (stormId) => {
  emit('select-storm', stormId)
}
</script>

<style scoped>
/* Tailwind自定义样式 */
.bg-dark-card {
  background-color: #161B22;
}

.border-dark-border {
  border-color: #30363D;
}

.text-primary {
  color: #165DFF;
}

.text-danger {
  color: #F53F3F;
}

.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}

/* 动画效果 */
.typhoon-item {
  transition: all 0.3s ease;
}

.typhoon-item:hover {
  transform: translateY(-2px);
}

.content-auto {
  content-visibility: auto;
}


.card-shadow {
  box-shadow: 0 0 15px rgba(22, 93, 255, 0.1);
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, #165DFF, #36BFFA);
}
</style>
