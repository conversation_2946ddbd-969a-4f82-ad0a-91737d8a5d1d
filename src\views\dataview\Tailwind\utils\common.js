import { ElMessage } from 'element-plus'

/**
 * 格式化日期时间
 * @param {string} dateTimeStr - 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
export const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '未知'

  try {
    const date = new Date(dateTimeStr)
    if (date.toString() === 'Invalid Date') return dateTimeStr

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  } catch (error) {
    return dateTimeStr
  }
}

/**
 * 更新当前时间
 * @returns {string} 当前时间字符串
 */
export const getCurrentTime = () => {
  const now = new Date()
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
    timeZone: 'Asia/Shanghai'
  }
  return now.toLocaleString('zh-CN', options)
}

/**
 * 显示错误消息
 * @param {string} message - 错误消息
 */
export const showError = (message) => {
  console.error(message)
  ElMessage.error(message)
}

/**
 * 显示成功消息
 * @param {string} message - 成功消息
 */
export const showSuccess = (message) => {
  ElMessage.success(message)
}

/**
 * 显示警告消息
 * @param {string} message - 警告消息
 */
export const showWarning = (message) => {
  ElMessage.warning(message)
}

/**
 * 显示信息消息
 * @param {string} message - 信息消息
 */
export const showInfo = (message) => {
  ElMessage.info(message)
}

/**
 * 生成随机数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @param {number} decimals - 小数位数
 * @returns {number} 随机数
 */
export const randomNumber = (min, max, decimals = 0) => {
  const random = Math.random() * (max - min) + min
  return decimals > 0 ? parseFloat(random.toFixed(decimals)) : Math.floor(random)
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, limit) => {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 检查是否为空值
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export const isEmpty = (value) => {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}
