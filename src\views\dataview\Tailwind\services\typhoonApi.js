import { API_CONFIG } from '../config/constants'
import { showError } from '../utils/common'

/**
 * 台风API服务类
 */
class TyphoonApiService {
  /**
   * 获取台风列表
   * @returns {Promise<Object>} API响应数据
   */
  async getStormList() {
    try {
      const url = `${API_CONFIG.stormListUrl}?basin=${API_CONFIG.basin}&year=${API_CONFIG.year}&key=${API_CONFIG.key}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.code !== "200") {
        throw new Error(`API error! code: ${data.code}`)
      }
      
      return data
    } catch (error) {
      showError(`获取台风列表失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 获取台风预测数据
   * @param {string} stormId - 台风ID
   * @returns {Promise<Object>} API响应数据
   */
  async getStormForecast(stormId) {
    try {
      const url = `${API_CONFIG.stormForecastUrl}?stormid=${stormId}&key=${API_CONFIG.key}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.code !== "200") {
        throw new Error(`API error! code: ${data.code}`)
      }
      
      return data
    } catch (error) {
      showError(`获取台风详情失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 过滤活跃台风
   * @param {Array} storms - 台风列表
   * @returns {Array} 活跃台风列表
   */
  filterActiveStorms(storms) {
    return storms.filter(storm => storm.isActive === "1")
  }

  /**
   * 验证台风数据
   * @param {Object} storm - 台风数据
   * @returns {boolean} 是否有效
   */
  validateStormData(storm) {
    return storm && storm.id && storm.name
  }

  /**
   * 验证预测数据
   * @param {Array} forecast - 预测数据
   * @returns {boolean} 是否有效
   */
  validateForecastData(forecast) {
    return Array.isArray(forecast) && forecast.length > 0
  }
}

// 创建单例实例
export const typhoonApi = new TyphoonApiService()

// 导出类以便测试
export { TyphoonApiService }
