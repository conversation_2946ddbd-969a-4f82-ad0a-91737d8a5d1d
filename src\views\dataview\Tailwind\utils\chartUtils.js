import { COLORS } from '../config/constants'

// 基础图表配置
const baseChartConfig = {
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: { 
    type: 'category', 
    show: false, 
    data: ['', '', '', '', '', '', ''] 
  },
  yAxis: { 
    type: 'value', 
    show: false 
  }
}

// 创建线性图表配置
export const createLineChartOption = (data, color, min, max, smooth = true) => ({
  ...baseChartConfig,
  yAxis: { ...baseChartConfig.yAxis, min, max },
  series: [{
    type: 'line',
    data,
    lineStyle: { color, width: 2 },
    areaStyle: { color: `${color}1A` }, // 添加透明度
    symbol: 'none',
    smooth
  }]
})

// 创建柱状图表配置
export const createBarChartOption = (data, color, min, max) => ({
  ...baseChartConfig,
  yAxis: { ...baseChartConfig.yAxis, min, max },
  series: [{
    type: 'bar',
    data,
    itemStyle: { color, borderRadius: [4, 4, 0, 0] },
    barWidth: '60%'
  }]
})

// 设备图表配置工厂函数
export const createChartOption = (type, data) => {
  const chartConfigs = {
    temperature: () => createLineChartOption(
      [25.2, 25.8, 26.1, 26.3, 26.5, 26.4, 26.5],
      COLORS.warning,
      24,
      28
    ),
    humidity: () => createLineChartOption(
      [72, 74, 76, 75, 77, 78, 78],
      COLORS.secondary,
      65,
      85
    ),
    windSpeed: () => createLineChartOption(
      [15.2, 16.5, 17.8, 18.2, 18.5, 18.3, 18.2],
      COLORS.primary,
      12,
      22
    ),
    pressure: () => createLineChartOption(
      [1004.2, 1003.8, 1003.5, 1003.0, 1002.5, 1002.2, 1002.0],
      COLORS.purple,
      1000,
      1006
    ),
    rainfall: () => createBarChartOption(
      [0, 2.5, 5.3, 8.2, 12.5, 18.7, 25.8],
      COLORS.blue,
      0,
      30
    ),
    wave: () => createLineChartOption(
      [2.1, 2.8, 3.2, 3.5, 3.2, 2.9, 3.2],
      COLORS.cyan,
      0,
      5
    ),
    visibility: () => createLineChartOption(
      [6.2, 7.5, 8.1, 8.5, 9.2, 8.8, 8.5],
      COLORS.yellow,
      0,
      15
    )
  }

  return chartConfigs[type] ? chartConfigs[type]() : null
}

// 台风路径图表配置
export const createTyphoonPathChart = (forecast, typhoonTypeMapping) => {
  if (!forecast || forecast.length === 0) {
    return null
  }

  // 提取经纬度数据
  const lats = forecast.map(point => parseFloat(point.lat) || 0)
  const lons = forecast.map(point => parseFloat(point.lon) || 0)

  // 计算经纬度范围
  const minLat = Math.min(...lats) - 1
  const maxLat = Math.max(...lats) + 1
  const minLon = Math.min(...lons) - 1
  const maxLon = Math.max(...lons) + 1

  return {
    backgroundColor: 'transparent',
    grid: {
      left: '15%',
      right: '15%',
      top: '10%',
      bottom: '10%'
    },
    xAxis: {
      type: 'value',
      name: '经度(°E)',
      nameTextStyle: { color: 'rgba(255, 255, 255, 0.7)' },
      min: minLon,
      max: maxLon,
      axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisTick: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisLabel: { color: 'rgba(255, 255, 255, 0.7)' },
      splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
    },
    yAxis: {
      type: 'value',
      name: '纬度(°N)',
      nameTextStyle: { color: 'rgba(255, 255, 255, 0.7)' },
      min: minLat,
      max: maxLat,
      axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisTick: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisLabel: { color: 'rgba(255, 255, 255, 0.7)' },
      splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: COLORS.primary,
      textStyle: { color: '#fff' },
      formatter: (params) => {
        const point = forecast[params.dataIndex]
        if (!point) return ''

        const date = new Date(point.fxTime || '')
        const timeStr = date.toString() !== 'Invalid Date'
          ? `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:00`
          : '时间未知'

        return [
          `${timeStr}`,
          `位置: ${point.lat || '--'}°N, ${point.lon || '--'}°E`,
          `气压: ${point.pressure || '--'} hPa`,
          `风速: ${point.windSpeed || '--'} m/s`,
          `等级: ${point.type ? (typhoonTypeMapping[point.type] || point.type) : '--'}`
        ].join('<br/>')
      }
    },
    series: [{
      type: 'line',
      data: forecast.map(point => [
        parseFloat(point.lon) || 0,
        parseFloat(point.lat) || 0
      ]),
      lineStyle: {
        color: COLORS.primary,
        width: 3
      },
      itemStyle: {
        color: (params) => {
          return params.dataIndex === 0 ? COLORS.danger : COLORS.primary
        },
        borderColor: '#fff',
        borderWidth: 2
      },
      symbol: 'circle',
      symbolSize: (_, params) => {
        return params.dataIndex === 0 ? 12 : 8
      },
      markPoint: {
        data: [{
          coord: [parseFloat(forecast[0].lon) || 0, parseFloat(forecast[0].lat) || 0],
          symbol: 'pin',
          symbolSize: 50,
          itemStyle: { color: COLORS.danger },
          label: {
            show: true,
            position: 'top',
            formatter: '当前位置',
            color: '#fff',
            fontSize: 12
          }
        }]
      }
    }]
  }
}
