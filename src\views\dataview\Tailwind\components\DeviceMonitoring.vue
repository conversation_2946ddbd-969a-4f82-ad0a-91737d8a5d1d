<template>
  <div class="h-[50%] overflow-y-auto">
    <div class="grid grid-cols-2 xl:grid-cols-3 gap-3">
      <!-- 温度监测 -->
      <DeviceCard
        type="temperature"
        :config="deviceConfigs.temperature"
        :value="deviceData.temperature"
        :chart-option="temperatureChartOption"
        trend="较昨日 ↓0.3°C"
      />

      <!-- 湿度监测 -->
      <DeviceCard
        type="humidity"
        :config="deviceConfigs.humidity"
        :value="deviceData.humidity"
        :chart-option="humidityChartOption"
        trend="较昨日 ↑5%"
      />

      <!-- 风速监测 -->
      <DeviceCard
        type="windSpeed"
        :config="deviceConfigs.windSpeed"
        :value="deviceData.windSpeed"
        :chart-option="windSpeedChartOption"
        trend="风力等级: 7级"
      />

      <!-- 风向监测 -->
      <DeviceCard
        type="windDirection"
        :config="deviceConfigs.windDirection"
        :value="{ direction: deviceData.windDirection, text: deviceData.windDirectionText }"
      />

      <!-- 气压监测 -->
      <DeviceCard
        type="pressure"
        :config="deviceConfigs.pressure"
        :value="deviceData.pressure"
        :chart-option="pressureChartOption"
        trend="趋势: ↓0.5 hPa"
      />

      <!-- 降雨量监测 -->
      <DeviceCard
        type="rainfall"
        :config="deviceConfigs.rainfall"
        :value="deviceData.rainfall"
        :chart-option="rainfallChartOption"
        trend="24小时累计"
      />

      <!-- 海浪高度监测 -->
      <DeviceCard
        type="wave"
        :config="deviceConfigs.wave"
        :value="deviceData.waveHeight"
        :chart-option="waveChartOption"
        trend="最大浪高: 4.2m"
      />

      <!-- 能见度监测 -->
      <DeviceCard
        type="visibility"
        :config="deviceConfigs.visibility"
        :value="deviceData.visibility"
        :chart-option="visibilityChartOption"
        trend="等级: 良好"
      />
    </div>
  </div>
</template>

<script setup>
import DeviceCard from './DeviceCard.vue'
import { DEVICE_TYPES } from '../config/constants'

// Props
const props = defineProps({
  deviceData: {
    type: Object,
    required: true
  },
  temperatureChartOption: {
    type: Object,
    required: true
  },
  humidityChartOption: {
    type: Object,
    required: true
  },
  windSpeedChartOption: {
    type: Object,
    required: true
  },
  pressureChartOption: {
    type: Object,
    required: true
  },
  rainfallChartOption: {
    type: Object,
    required: true
  },
  waveChartOption: {
    type: Object,
    required: true
  },
  visibilityChartOption: {
    type: Object,
    required: true
  }
})

// 设备配置
const deviceConfigs = {
  temperature: DEVICE_TYPES.TEMPERATURE,
  humidity: DEVICE_TYPES.HUMIDITY,
  windSpeed: DEVICE_TYPES.WIND_SPEED,
  windDirection: DEVICE_TYPES.WIND_DIRECTION,
  pressure: DEVICE_TYPES.PRESSURE,
  rainfall: DEVICE_TYPES.RAINFALL,
  wave: DEVICE_TYPES.WAVE,
  visibility: DEVICE_TYPES.VISIBILITY
}
</script>

<style scoped>
@import '../styles/common.css';
</style>
